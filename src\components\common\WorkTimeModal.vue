<!-- eslint-disable antfu/top-level-function -->
<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import type { CascaderOption } from 'naive-ui'
import { DeleteOutlined, PlusOutlined, UndoOutlined } from '@ant-design/icons-vue'
import type { SelectValue } from 'ant-design-vue/es/select'
import type { ValueType } from 'ant-design-vue/es/vc-cascader/Cascader'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import { ATTENDANCE_TYPE, PROJECT_MENU_TYPE } from '~@/api/attendance'
import type { AttendanceItem, AttendanceUpdateParams } from '~@/api/attendance'
import { YYYYMMDDHHMMSSRegex, convertSecondsToTime, convertTimeToSeconds, getDurationTime } from '~@/utils/apiTimer'
import type { BreakTime, ProjectComboItem } from '~@/api/company/project'
import { ModalType } from '~@/enums/system-status-enum'
import { useEmployees } from '~@/composables/employee/useEmployees'

// Props và Emits
const props = defineProps({
  show: {
    type: Boolean,
    default: true,
  },
  attendanceItem: Object as () => AttendanceItem | null,
  selectedDate: Object as () => Dayjs,
  type: {
    type: String,
    default: ModalType.EDIT,
  },
  isRepresentative: {
    type: Boolean,
    default: false,
  },
  projectId: {
    type: String,
  },
})

const emit = defineEmits(['update:show', 'saveAttendance', 'cancel'])

const { t } = useI18n()
const messageNotify = useMessage()
const { employeesOptions, fetchEmployees } = useEmployees()
const { projects } = useProjectStore()

// Reactive state
const showAttendanceModal = computed({
  get: () => props.show,
  set: value => emit('update:show', value),
})

const loading = ref(false)
// const dateOfCheckInTime = ref<string | null>(null)
const isInputWorkplaceVisible = ref(false)
const selectedAttendanceType = ref<number>(ATTENDANCE_TYPE.CURRENT_PROJECT)
const initialCascaderValue = ref<string[]>([])
const totalWorkTime = ref<string>('00:00')
const totalBreakTime = ref<string>('00:00')
const formState = reactive<AttendanceUpdateParams>({
  checkInTime: '',
  checkOutTime: '',
  breakList: [],
  description: '',
  workingLocation: '',
  projectId: '',
  totalOverTime: 0,
  employeeIds: [],
})

const projectMenuOptions = ref<CascaderOption[]>([
  {
    value: PROJECT_MENU_TYPE.CURRENT_PROJECT,
    label: t('select.current-project'),
  },
  {
    value: PROJECT_MENU_TYPE.OTHER_PROJECT,
    label: t('select.other-project'),
    children: [],
  },
  {
    value: PROJECT_MENU_TYPE.BUSINESS,
    label: t('select.business'),
  },
  {
    value: PROJECT_MENU_TYPE.REMOTE,
    label: t('select.remote'),
  },
  {
    value: PROJECT_MENU_TYPE.CUSTOM_WORKPLACE,
    label: t('enterWorkplace'),
  },
] as CascaderOption[])

const breakTimeDefault = computed(() => {
  const projectItem: ProjectComboItem | undefined = projects.find((item: ProjectComboItem) => item.id === formState.projectId)
  if (projectItem) {
    const breakList: BreakTime[] = projectItem?.defaultWorkShift?.breakTimes ?? []
    return breakList.map(b => ({
      breakInTime: b.startBreak,
      breakOutTime: b.endBreak,
    }))
  }
  return []
})

// Helper function to get cascader value from working location
const getCascaderValueFromWorkingLocation = (workingLocation: string | undefined): string[] => {
  if (!workingLocation)
    return []

  switch (workingLocation) {
    case 'BUSINESS':
      return [PROJECT_MENU_TYPE.BUSINESS]
    case 'REMOTE':
      return [PROJECT_MENU_TYPE.REMOTE]
    case 'current_project':
      return [PROJECT_MENU_TYPE.CURRENT_PROJECT]
    default: {
      // Check if it's an address from other project
      const projectItem = projects.find((item: ProjectComboItem) => item.address === workingLocation)
      if (projectItem)
        return [PROJECT_MENU_TYPE.OTHER_PROJECT, workingLocation]

      // Otherwise it's custom workplace
      return [PROJECT_MENU_TYPE.CUSTOM_WORKPLACE]
    }
  }
}

async function addBreakTime() {
  if (formState.breakList?.length === 3) {
    messageNotify.error(t('error.youReachMaxBreakTime'))
    return
  }
  formState.breakList?.push({
    breakInTime: undefined,
    breakOutTime: undefined,
  })
  updateTotalWorkTime()
  updateTotalBreakTime()
}

function removeBreakTime(index: number) {
  formState.breakList!.splice(index, 1)
  updateTotalWorkTime()
  updateTotalBreakTime()
}

function updateTotalBreakTime() {
  if (!formState.breakList || formState.breakList.length === 0)
    totalBreakTime.value = '00:00'

  let totalBreakMinutes = 0
  if (formState.breakList) {
    formState.breakList.forEach((breakTime) => {
      if (breakTime.breakInTime && breakTime.breakOutTime) {
        const startSeconds = convertTimeToSeconds(breakTime.breakInTime)
        const endSeconds = convertTimeToSeconds(breakTime.breakOutTime)
        const duration = endSeconds - startSeconds
        totalBreakMinutes += duration
      }
    })
  }
  totalBreakTime.value = convertSecondsToTime(totalBreakMinutes).substring(0, 5)
}

// Calculate total work time
function updateTotalWorkTime() {
  if (formState.checkInTime && formState.checkOutTime) {
    let grossSeconds = getDurationTime(formState.checkInTime, formState.checkOutTime)
    let totalBreakSeconds = 0
    if (formState.breakList) {
      formState.breakList.forEach((breakTime) => {
        if (breakTime.breakInTime && breakTime.breakOutTime) {
          const startBreak = breakTime.breakInTime
          const endBreak = breakTime.breakOutTime
          const duration = getDurationTime(startBreak, endBreak)
          totalBreakSeconds += duration
        }
      })
    }
    grossSeconds -= totalBreakSeconds
    totalWorkTime.value = convertSecondsToTime(grossSeconds).substring(0, 5)
  }
  else {
    totalWorkTime.value = '00:00'
  }
}

const resetFormState = () => {
  formState.checkInTime = ''
  formState.checkOutTime = ''
  formState.breakList = []
  formState.description = ''
  formState.workingLocation = ''
  formState.projectId = undefined
  formState.employeeIds = []
  initialCascaderValue.value = []
  isInputWorkplaceVisible.value = false
}

function isFormValid() {
  if (!formState.projectId) {
    messageNotify.error(t('error.projectRequired'))
    return false
  }

  // if (!formState.checkInTime) {
  //   messageNotify.error(t('error.checkInTimeRequired'))
  //   return false
  // }

  // if (!formState.checkOutTime) {
  //   messageNotify.error(t('error.checkOutTimeRequired'))
  //   return false
  // }

  const currentTime = dayjs()

  if (formState.checkInTime && formState.checkOutTime) {
    const checkInTime = dayjs(formState.checkInTime)
    const checkOutTime = dayjs(formState.checkOutTime)
    if (checkInTime.isAfter(checkOutTime)) {
      messageNotify.error(t('error.checkInTimeMustBeBeforeCheckOutTime'))
      return false
    }

    const limitedCheckOutDate = checkInTime.add(1, 'day').format('YYYY-MM-DD')
    const limitCheckOutTime = dayjs(`${limitedCheckOutDate} 23:59:59`)
    if (checkOutTime.isAfter(limitCheckOutTime)) {
      messageNotify.error(t('error.checkOutTimeMustBeBefore2359OfNextOneDayOfCheckInDate'))
      return false
    }

    if (checkOutTime.isAfter(currentTime)) {
      messageNotify.error(t('error.checkOutTimeMustBeBeforeCurrentTime'))
      return false
    }
  }

  else if (formState.checkInTime && dayjs(formState.checkInTime).isAfter(currentTime)) {
    messageNotify.error(t('error.checkInTimeMustBeBeforeCurrentTime'))
    return false
  }

  const currentTime = dayjs()
  if (checkOutTime.isAfter(currentTime)) {

    messageNotify.error(t('error.checkOutTimeMustBeBeforeCurrentTime'))
    return false
  }

  return true
}

async function onSaveAttendance() {
  if (!isFormValid())
    return

  loading.value = true
  if (formState.workingLocation === 'current_project')
    formState.workingLocation = projects.find((item: ProjectComboItem) => item.id === formState.projectId)?.address ?? ''
  try {
    const attendanceItem: AttendanceUpdateParams = {}
    const employeeShiftId = props.attendanceItem?.employeeShiftId ?? ''
    if (formState.checkInTime)
      attendanceItem.checkInTime = `${formState.checkInTime}:00`

    if (formState.checkOutTime)
      attendanceItem.checkOutTime = `${formState.checkOutTime}:00`

    if (formState.breakList) {
      attendanceItem.breakList = formState.breakList.map(b => ({
        breakInTime: b.breakInTime,
        breakOutTime: b.breakOutTime,
      }))
    }
    attendanceItem.description = formState.description
    attendanceItem.workingLocation = formState.workingLocation
    attendanceItem.projectId = formState.projectId
    attendanceItem.attendanceType = selectedAttendanceType.value
    if (formState.checkInTime)
      attendanceItem.workingDate = dayjs(formState.checkInTime).format('YYYY-MM-DD')
    if (props.isRepresentative)
      attendanceItem.employeeIds = formState.employeeIds

    emit('saveAttendance', employeeShiftId, attendanceItem)
    resetFormState()
    closeModal()
  }
  catch (error) {
  }
  finally {
    loading.value = false
  }
}

function initData() {
  // Initialize form fields
  if (props.attendanceItem) {
    const checkInDate = props.attendanceItem.workingDate
    let checkOutDate = checkInDate
    const checkInTime = props.attendanceItem.checkInTime
    let checkOutTime = props.attendanceItem.checkOutTime
    const [hour, minute, second] = checkOutTime?.split(':') ?? []
    if (hour && minute && second) {
      let hourNumber = Number(hour)
      if (hourNumber >= 24) {
        hourNumber -= 24
        checkOutDate = dayjs(checkInDate).add(1, 'day').format('YYYY-MM-DD')
        checkOutTime = `${hourNumber}:${minute}:${second}`
      }
    }
    const checkInTimeTmp = `${checkInDate} ${checkInTime}`
    const checkOutTimeTmp = `${checkOutDate} ${checkOutTime}`
    if (YYYYMMDDHHMMSSRegex.test(checkInTimeTmp))
      formState.checkInTime = checkInTimeTmp.slice(0, 16)
    if (YYYYMMDDHHMMSSRegex.test(checkOutTimeTmp))
      formState.checkOutTime = dayjs(checkOutTimeTmp, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD HH:mm')

    formState.breakList = props.attendanceItem.breakList
    if (formState.breakList) {
      formState.breakList.forEach((breakTime) => {
        if (breakTime.breakInTime && breakTime.breakOutTime) {
          breakTime.breakInTime = breakTime.breakInTime.slice(0, 5)
          breakTime.breakOutTime = breakTime.breakOutTime.slice(0, 5)
        }
      })
    }
    formState.description = props.attendanceItem.description ?? ''
    formState.workingLocation = props.attendanceItem.workingLocation ?? ''
    formState.projectId = props.attendanceItem.projectId ?? ''

    // Set the cascader value and input visibility based on working location
    const cascaderValue = getCascaderValueFromWorkingLocation(props.attendanceItem.workingLocation ?? '')
    initialCascaderValue.value = cascaderValue

    // Show input field for custom workplace
    if (cascaderValue.length === 1 && cascaderValue[0] === PROJECT_MENU_TYPE.CUSTOM_WORKPLACE)
      isInputWorkplaceVisible.value = true
    else
      isInputWorkplaceVisible.value = false
  }
  else {
    if (props.selectedDate) {
      const selectedDate = props.selectedDate?.format('YYYY-MM-DD')
      formState.checkInTime = `${selectedDate} 09:00`
      formState.checkOutTime = `${selectedDate} 17:00`
    }
    else {
      formState.checkInTime = ''
      formState.checkOutTime = ''
    }
  }

  if (props.projectId)
    formState.projectId = props.projectId

  updateTotalBreakTime()
  updateTotalWorkTime()
}

function closeModal() {
  showAttendanceModal.value = false
  resetFormState()
}

const projectOptions = computed(() => {
  return projects.map((item: ProjectComboItem) => ({
    label: item.name,
    value: item.id,
  }))
})

const handleProjectChange = (value: string | null | undefined) => {
  formState.breakList = []
  if (value) {
    isInputWorkplaceVisible.value = false
    formState.workingLocation = undefined
    formState.projectId = value
  }
}

watch(projects, () => {
  if (projectOptions.value) {
    projectMenuOptions.value[1].children = projectOptions.value.map((projOption: { label: string; value: string }) => {
      const projectItem = projects.find((item: ProjectComboItem) => item.id === projOption.value)
      return {
        label: projOption.label,
        value: projectItem?.address ?? '',
      }
    })
  }
})

// Helper function to map PROJECT_MENU_TYPE to ATTENDANCE_TYPE
const getAttendanceType = (projectMenuType: string): number => {
  switch (projectMenuType) {
    case PROJECT_MENU_TYPE.CURRENT_PROJECT:
      return ATTENDANCE_TYPE.CURRENT_PROJECT
    case PROJECT_MENU_TYPE.OTHER_PROJECT:
      return ATTENDANCE_TYPE.OTHER_PROJECT
    case PROJECT_MENU_TYPE.BUSINESS:
      return ATTENDANCE_TYPE.BUSINESS
    case PROJECT_MENU_TYPE.REMOTE:
      return ATTENDANCE_TYPE.REMOTE
    case PROJECT_MENU_TYPE.CUSTOM_WORKPLACE:
      return ATTENDANCE_TYPE.CUSTOM_WORKPLACE
    default:
      return ATTENDANCE_TYPE.CURRENT_PROJECT
  }
}

function handleUpdateValue(value: ValueType) {
  isInputWorkplaceVisible.value = false // Reset visibility by default
  let selectedValueOne: string = ''

  if (Array.isArray(value) && value.length > 0) {
    if (value.length > 1) {
      selectedValueOne = value[0].toString()
      formState.workingLocation = value[1].toString()
    }
    else {
      selectedValueOne = value[0].toString()
      switch (selectedValueOne) {
        case PROJECT_MENU_TYPE.CUSTOM_WORKPLACE:
          isInputWorkplaceVisible.value = true
          formState.workingLocation = '' // Clear for manual input, a-input will bind to this
          break
        case PROJECT_MENU_TYPE.REMOTE:
          formState.workingLocation = 'REMOTE'
          break
        case PROJECT_MENU_TYPE.BUSINESS:
          formState.workingLocation = 'BUSINESS'
          break
        case PROJECT_MENU_TYPE.CURRENT_PROJECT: {
          const currentProject = projects.find((p: ProjectComboItem) => p.id === formState.projectId)
          if (currentProject)
            formState.workingLocation = currentProject.address ?? null
          else
            formState.workingLocation = ''
          break
        }
        default:
          if (selectedValueOne && selectedValueOne !== 'other_project')
            formState.workingLocation = selectedValueOne
          break
      }
    }
    // Set the attendance type based on the selected value
    selectedAttendanceType.value = getAttendanceType(selectedValueOne)
    // Update the cascader value for display
    initialCascaderValue.value = value as string[]
  }
}

const handleBackToProjectMenu = () => {
  isInputWorkplaceVisible.value = false
  formState.workingLocation = undefined
}

// const handleBreakTimeSelect = (time: Dayjs, index: number, type: 'breakInTime' | 'breakOutTime') => {
//   formState.breakList![index][type] = time.format('HH:mm')
//   updateTotalWorkTime()
// }

const autoSetBreakTime = () => {
  const checkInTimeStr = dayjs(formState.checkInTime).format('HH:mm:ss')
  const checkInTime = dayjs(checkInTimeStr, 'HH:mm:ss')
  const checkOutTimeStr = dayjs(formState.checkOutTime).format('HH:mm:ss')
  const checkOutTime = dayjs(checkOutTimeStr, 'HH:mm:ss')
  formState.breakList = []
  breakTimeDefault.value.forEach((breakTime) => {
    const breakInTime = dayjs(breakTime.breakInTime, 'HH:mm:ss')
    const breakOutTime = dayjs(breakTime.breakOutTime, 'HH:mm:ss')
    if (breakInTime.isAfter(checkInTime) && breakOutTime.isBefore(checkOutTime)) {
      formState.breakList!.push({
        breakInTime: breakTime.breakInTime,
        breakOutTime: breakTime.breakOutTime,
      })
    }
  })
}

const handleCheckInTimeSelect = (time: Dayjs) => {
  formState.checkInTime = time.format('YYYY-MM-DD HH:mm')
  updateTotalWorkTime()
  autoSetBreakTime()
}

const handleCheckOutTimeSelect = (time: Dayjs) => {
  formState.checkOutTime = time.format('YYYY-MM-DD HH:mm')
  updateTotalWorkTime()
  autoSetBreakTime()
}

// Lifecycle hooks
onMounted(async () => {
  if (props.show)
    initData()

  fetchEmployees()
})

// Watchers
watch(() => props.show, (newVal) => {
  if (newVal)
    initData()
})
</script>

<template>
  <a-modal v-model:open="showAttendanceModal" width="650px" :destroy-on-close="true" @cancel="closeModal">
    <template #title>
      <div class="flex justify-center items-center">
        <a-typography-title v-if="props.type === ModalType.EDIT" :level="4" class="text-gray-800 font-semibold">
          {{ t('title.editAttendance') }}
        </a-typography-title>
        <a-typography-title v-else :level="4" class="text-gray-800 font-semibold">
          {{ t('title.addAttendance') }}
        </a-typography-title>
      </div>
    </template>
    <div class="space-y-6">
      <div class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-semibold text-gray-800 mb-2">{{ t('projectName') }}</label>
            <ProjectSelect
              v-model:value="formState.projectId"
              :disabled="
                props.attendanceItem?.scheduledStartTime !== null && props.attendanceItem?.checkInTime === null
              "
              :date="props.selectedDate?.format('YYYY-MM-DD')"
              @change="(value: SelectValue) => handleProjectChange(value?.toString())"
            />
          </div>
          <div>
            <label class="block text-sm font-semibold text-gray-800 mb-2">{{ t('workplace') }}</label>
            <a-cascader
              v-if="!isInputWorkplaceVisible"
              :placeholder="t('placeholder.select', { msg: t('workplace') })"
              :options="projectMenuOptions"
              :value="initialCascaderValue"
              class="w-full"
              @change="handleUpdateValue"
            />
            <a-input
              v-else
              v-model:value="formState.workingLocation"
              :placeholder="t('placeholder.enter', { msg: t('workplace') })"
              class="w-full"
            >
              <template #suffix>
                <UndoOutlined class="cursor-pointer hover:text-blue-600 text-gray-500" @click="handleBackToProjectMenu" />
              </template>
            </a-input>
          </div>
        </div>
      </div>

      <div v-if="props.isRepresentative" class="space-y-4">
        <div class="grid grid-cols-1 gap-4">
          <div>
            <label class="block text-sm font-semibold text-gray-800 mb-2">{{ t('employee') }}</label>
            <a-select
              v-model:value="formState.employeeIds"
              :options="employeesOptions"
              :placeholder="t('placeholder.select', { msg: t('employee') })"
              class="w-full"
              mode="multiple"
            />
          </div>
        </div>
      </div>

      <!-- Time tracking section -->
      <div class="space-y-4">
        <div class="flex items-center justify-end">
          <div class="flex items-center space-x-2">
            <div class="text-sm font-medium">
              <span class="text-gray-600 font-medium">{{ t('totalWorkingTime') }}:&nbsp;</span>
              <span class="text-blue-600 font-bold text-base">{{ totalWorkTime }}</span>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <div>
            <span class="block text-sm font-semibold text-gray-800 mb-2">{{ t('checkInTime') }}</span>
            <a-date-picker
              v-model:value="formState.checkInTime"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm"
              class="w-full" show-time
              :minute-step="5"
              @select="(time: Dayjs) => handleCheckInTimeSelect(time)"
            />
          </div>
          <div>
            <span class="block text-sm font-semibold text-gray-800 mb-2">{{ t('checkOutTime') }}</span>
            <a-date-picker
              v-model:value="formState.checkOutTime"
              value-format="YYYY-MM-DD HH:mm"
              format="YYYY-MM-DD HH:mm"
              :placeholder="t('placeholder.select', { msg: t('time') })"
              class="w-full"
              show-time
              :minute-step="5"
              @select="(time: Dayjs) => handleCheckOutTimeSelect(time)"
            />
          </div>
        </div>
      </div>

      <!-- Break time section -->
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center justify-between">
            <label class="block text-sm font-semibold text-gray-800 mb-1">{{ t('breakTime') }}</label>
          </div>
          <div class="flex items-center space-x-2">
            <div class="text-sm font-medium">
              <span class="text-gray-600 font-medium">{{ t('totalBreakTime') }}:&nbsp;</span>
              <span class="text-green-600 font-bold">{{ totalBreakTime }}</span>
            </div>
            <a-button size="small" type="primary" class="flex items-center justify-center" @click="addBreakTime">
              <template #icon>
                <PlusOutlined />
              </template>
            </a-button>
          </div>
        </div>

        <div class="space-y-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
          <div v-if="formState.breakList!.length === 0" class="text-center py-8 bg-gray-50 rounded-lg">
            <div class="i-carbon-time mb-3 mx-auto text-gray-400 text-3xl" />
            <p class="text-sm text-gray-600 font-medium">
              {{ t('noBreakTime') }}
            </p>
          </div>

          <div
            v-for="(breakTime, index) in formState.breakList" :key="index"
            class="flex items-center justify-between mb-3"
          >
            <div class="grid grid-cols-2 gap-3 w-full">
              <div>
                <label class="block text-xs text-gray-500 mb-1">{{ t('startTime') }}</label>
                <!-- <a-time-picker
                  v-model:value="breakTime.breakInTime"
                  format="HH:mm"
                  value-format="HH:mm"
                  :placeholder="t('placeholder.select', { msg: t('time') })"
                  class="w-full"
                  :minute-step="5"
                  @select="(time: Dayjs) => handleBreakTimeSelect(time, index, 'breakInTime')"
                /> -->
                <TimePicker
                  v-model:value="breakTime.breakInTime"
                  :minute-step="5"
                  value-format="HH:mm"
                  value-type="string"
                  @change="() => updateTotalWorkTime()"
                />
              </div>
              <div>
                <label class="block text-xs text-gray-500 mb-1">{{ t('endTime') }}</label>
                <!-- <a-time-picker
                  v-model:value="breakTime.breakOutTime"
                  value-format="HH:mm"
                  format="HH:mm"
                  :placeholder="t('placeholder.select', { msg: t('time') })"
                  class="w-full"
                  :minute-step="5"
                  @select="(time: Dayjs) => handleBreakTimeSelect(time, index, 'breakOutTime')"
                /> -->
                <TimePicker
                  v-model:value="breakTime.breakOutTime"
                  :minute-step="5"
                  value-format="HH:mm"
                  value-type="string"
                  @change="() => updateTotalWorkTime()"
                />
              </div>
            </div>
            <div class="flex items-center justify-center">
              <a-button size="small" class="flex items-center justify-center" type="text" @click="removeBreakTime(index)">
                <template #icon>
                  <DeleteOutlined class="text-red-500 hover:text-red-600" />
                </template>
              </a-button>
            </div>
          </div>
        </div>
      </div>

      <!-- Note section -->
      <div class="space-y-2">
        <label class="block text-sm font-semibold text-gray-800 mb-2">{{ t('note') }}</label>
        <a-textarea
          v-model:value="formState.description"
          :placeholder="t('placeholder.enter', { msg: t('note') })"
          :rows="3"
          class="text-gray-700"
        />
      </div>
    </div>
    <template #footer>
      <div class="flex justify-end gap-3">
        <a-button class="font-medium text-gray-700" @click="closeModal">
          {{ t('button.cancel') }}
        </a-button>
        <a-button
          type="primary"
          :loading="loading"
          class="font-medium"
          @click="onSaveAttendance"
        >
          {{ props.type === ModalType.ADD ? t('button.add') : t('button.update') }}
        </a-button>
      </div>
    </template>
  </a-modal>
</template>
